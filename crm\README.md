# محلات أبو علاء - نظام إدارة الديون التجارية

نظام ويب متكامل لإدارة الديون التجارية باللغة العربية، مصمم خصيصاً للاستخدام الداخلي من قِبل الموظفين والمدراء.

## المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن بـ username/password
- صلاحيات متدرجة (مدير، موظف)
- حماية الوصول للصفحات حسب الدور

### 📊 لوحة التحكم
- إحصائيات شاملة: إجمالي الديون، المدفوع، المتبقي، المتأخر
- رسوم بيانية شهرية تفاعلية
- إشعارات للديون المتأخرة والمستحقة خلال 7 أيام

### 👥 إدارة العملاء
- نظام CRUD كامل للعملاء
- بحث متقدم بالاسم، الهاتف، والبريد
- عرض تفصيلي لكل عميل مع إحصائياته

### 💰 إدارة الديون
- إضافة ديون جديدة مع تفاصيل كاملة
- تتبع حالة الديون (نشط، مدفوع، متأخر، ملغي)
- تحديث تلقائي للحالات حسب تواريخ الاستحقاق
- فلترة متقدمة حسب العميل، التاريخ، والحالة

### 💳 إدارة الدفعات
- تسجيل دفعات جزئية أو كاملة
- طرق دفع متعددة (نقداً، تحويل بنكي، شيك، بطاقة ائتمان)
- تحديث تلقائي لحالة الديون بعد كل دفعة
- سجل كامل لجميع الدفعات

### 🧾 نظام الفواتير
- إنشاء فواتير تلقائياً عند إضافة الديون
- توليد PDF للفواتير بالعربية مع تصميم احترافي
- عرض معلومات العميل والدين والدفعات
- ختم "مدفوع" تلقائي عند اكتمال السداد

### 📈 التقارير والإحصائيات
- تقارير شهرية وسنوية
- فلترة حسب العميل، الحالة، وطريقة الدفع
- رسوم بيانية تفاعلية باستخدام Chart.js

## التقنيات المستخدمة

- **Backend**: Python 3.11 + Django 5.2
- **Database**: PostgreSQL (إنتاج) / SQLite (تطوير)
- **Frontend**: Bootstrap 5 RTL + JavaScript
- **Charts**: Chart.js
- **PDF Generation**: WeasyPrint
- **Deployment**: Docker + Google Cloud Run
- **Static Files**: WhiteNoise

## التثبيت والتشغيل

### المتطلبات
- Python 3.11+
- pip
- virtualenv (اختياري)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd abu_alaa_project
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
python manage.py migrate
```

5. **إنشاء superuser**
```bash
python manage.py createsuperuser
```

6. **إنشاء بيانات تجريبية (اختياري)**
```bash
python manage.py create_sample_data
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح التطبيق**
```
http://127.0.0.1:8000
```

## الاستخدام

### تسجيل الدخول
- استخدم بيانات الـ superuser المُنشأ
- أو استخدم: `admin` / `admin123` (إذا تم إنشاء البيانات التجريبية)

### إضافة عميل جديد
1. اذهب إلى "العملاء" → "إضافة عميل جديد"
2. املأ البيانات المطلوبة
3. احفظ

### إضافة دين
1. من صفحة العميل، اضغط "إضافة دين"
2. أو من "الديون" → "إضافة دين جديد"
3. املأ تفاصيل الدين
4. سيتم إنشاء فاتورة تلقائياً

### تسجيل دفعة
1. من تفاصيل الدين، اضغط "تسجيل دفعة"
2. أدخل المبلغ وطريقة الدفع
3. احفظ - سيتم تحديث حالة الدين تلقائياً

## الهيكل التنظيمي

```
abu_alaa_project/
├── abu_alaa_project/          # إعدادات Django الرئيسية
├── crm/                       # تطبيق إدارة الديون
│   ├── models.py             # نماذج قاعدة البيانات
│   ├── views.py              # منطق التطبيق
│   ├── urls.py               # مسارات URL
│   ├── admin.py              # إعدادات لوحة الإدارة
│   └── management/           # أوامر إدارية مخصصة
├── templates/                 # قوالب HTML
│   ├── base.html             # القالب الأساسي
│   ├── auth/                 # قوالب المصادقة
│   ├── customers/            # قوالب العملاء
│   ├── debts/                # قوالب الديون
│   ├── payments/             # قوالب الدفعات
│   └── invoices/             # قوالب الفواتير
├── static/                    # ملفات CSS/JS/Images
├── media/                     # ملفات المستخدمين
├── requirements.txt           # متطلبات Python
├── Dockerfile                # إعدادات Docker
└── deploy.md                 # دليل النشر
```

## النشر على الإنتاج

راجع ملف [deploy.md](deploy.md) للحصول على تعليمات مفصلة للنشر على Google Cloud Run.

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في GitHub.

---

**محلات أبو علاء** - نظام إدارة الديون التجارية الاحترافي 🏪
