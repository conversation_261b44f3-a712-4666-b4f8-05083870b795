{% extends 'base.html' %}

{% block title %}حذف العميل - {{ customer.name }} - محلات أبو علاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2 text-danger">
        <i class="fas fa-trash me-2"></i>
        حذف العميل - {{ customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'customer_detail' customer.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h6 class="alert-heading">تحذير!</h6>
                    <p class="mb-0">
                        هذا الإجراء لا يمكن التراجع عنه. سيتم حذف العميل وجميع البيانات المرتبطة به نهائياً.
                    </p>
                </div>
                
                <div class="mb-4">
                    <h6>معلومات العميل المراد حذفه:</h6>
                    <ul class="list-unstyled">
                        <li><strong>الاسم:</strong> {{ customer.name }}</li>
                        <li><strong>الهاتف:</strong> {{ customer.phone }}</li>
                        {% if customer.email %}
                        <li><strong>البريد:</strong> {{ customer.email }}</li>
                        {% endif %}
                        <li><strong>عدد الديون:</strong> {{ customer.debts.count }}</li>
                        <li><strong>إجمالي الديون:</strong> {{ customer.total_debt|floatformat:2 }} ريال</li>
                    </ul>
                </div>
                
                {% if customer.debts.count > 0 %}
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> هذا العميل لديه {{ customer.debts.count }} دين مسجل. 
                    سيتم حذف جميع الديون والدفعات المرتبطة به.
                </div>
                {% endif %}
                
                <form method="post" class="d-flex justify-content-between">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        نعم، احذف العميل نهائياً
                    </button>
                    
                    <a href="{% url 'customer_detail' customer.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
