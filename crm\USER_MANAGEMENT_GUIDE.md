# دليل إدارة المستخدمين - محلات أبو علاء
# User Management Guide - Abu Alaa Stores

## 🔑 بيانات تسجيل الدخول الحالية

### المستخدمين المتاحين:
| اسم المستخدم | كلمة المرور | النوع | الصلاحيات |
|--------------|-------------|-------|-----------|
| `admin` | `admin123` | مدير رئيسي | جميع الصلاحيات |
| `manager` | `manager123` | مدير | إدارة العملاء والديون |

### رابط تسجيل الدخول:
🌐 **http://localhost:8000/login/**

---

## 🚀 الطرق السريعة لإعادة التعيين

### 1. الطريقة الأسرع (مُوصى بها):
```bash
# انقر مرتين على الملف
reset_login.bat
```

### 2. من Command Prompt:
```bash
cd Z:\crm
python reset_password.py quick
```

### 3. إنشاء مستخدم جديد:
```bash
python manage.py createsuperuser
```

---

## 🔧 أداة إدارة المستخدمين المتقدمة

### تشغيل الأداة التفاعلية:
```bash
python reset_password.py
```

### الخيارات المتاحة:
1. **عرض جميع المستخدمين** - قائمة شاملة بالمستخدمين
2. **إعادة تعيين كلمة مرور** - تغيير كلمة مرور مستخدم موجود
3. **إنشاء مستخدم جديد** - إضافة مستخدم جديد
4. **حذف مستخدم** - إزالة مستخدم من النظام

### الأوامر المباشرة:
```bash
# عرض قائمة المستخدمين
python reset_password.py list

# إعادة تعيين سريعة
python reset_password.py quick

# عرض المساعدة
python reset_password.py help
```

---

## 👥 إدارة المستخدمين

### إنشاء مستخدم جديد:
```bash
# الطريقة الأولى: Django Admin
python manage.py createsuperuser

# الطريقة الثانية: أداة مخصصة
python reset_password.py
# ثم اختر الخيار 3
```

### تغيير كلمة مرور مستخدم موجود:
```bash
# استخدام الأداة المخصصة
python reset_password.py
# ثم اختر الخيار 2
```

### عرض جميع المستخدمين:
```bash
python reset_password.py list
```

---

## 🔒 أنواع المستخدمين والصلاحيات

### 1. المدير الرئيسي (Superuser):
- ✅ الوصول لجميع أجزاء النظام
- ✅ إدارة المستخدمين
- ✅ إعدادات النظام
- ✅ النسخ الاحتياطي
- ✅ التقارير والإحصائيات

### 2. المدير (Staff):
- ✅ إدارة العملاء
- ✅ إدارة الديون والمدفوعات
- ✅ إنشاء الفواتير
- ✅ عرض التقارير
- ❌ إدارة المستخدمين

### 3. المستخدم العادي:
- ✅ عرض العملاء
- ✅ عرض الديون
- ❌ تعديل البيانات
- ❌ حذف البيانات

---

## 🛠️ استكشاف مشاكل تسجيل الدخول

### المشاكل الشائعة:

#### 1. نسيان كلمة المرور:
```bash
# الحل السريع
reset_login.bat

# أو
python reset_password.py quick
```

#### 2. نسيان اسم المستخدم:
```bash
# عرض جميع المستخدمين
python reset_password.py list
```

#### 3. خطأ "Invalid username or password":
- تأكد من كتابة اسم المستخدم وكلمة المرور بشكل صحيح
- تأكد من عدم وجود مسافات إضافية
- جرب إعادة تعيين كلمة المرور

#### 4. المستخدم غير نشط:
```bash
# تفعيل المستخدم من Django Admin
python manage.py shell
>>> from django.contrib.auth.models import User
>>> user = User.objects.get(username='اسم_المستخدم')
>>> user.is_active = True
>>> user.save()
```

---

## 🔐 أمان كلمات المرور

### كلمات المرور الافتراضية:
⚠️ **تحذير:** كلمات المرور الافتراضية بسيطة لسهولة الاستخدام

### تغيير كلمة المرور لأمان أفضل:
1. سجل الدخول للنظام
2. اذهب إلى "تغيير كلمة المرور" من القائمة
3. أدخل كلمة مرور قوية جديدة

### نصائح لكلمة مرور قوية:
- ✅ 8 أحرف على الأقل
- ✅ مزيج من الأحرف الكبيرة والصغيرة
- ✅ أرقام ورموز خاصة
- ❌ تجنب المعلومات الشخصية

---

## 📋 سجل المستخدمين

### عرض معلومات مفصلة:
```bash
python reset_password.py list
```

### المعلومات المعروضة:
- اسم المستخدم
- البريد الإلكتروني
- حالة النشاط
- نوع المستخدم (مدير/عادي)
- آخر تسجيل دخول
- تاريخ إنشاء الحساب

---

## 🔄 النسخ الاحتياطي للمستخدمين

### حفظ قائمة المستخدمين:
```bash
# تصدير بيانات المستخدمين
python manage.py dumpdata auth.user > users_backup.json
```

### استعادة المستخدمين:
```bash
# استيراد بيانات المستخدمين
python manage.py loaddata users_backup.json
```

---

## 🚨 حالات الطوارئ

### في حالة فقدان جميع كلمات المرور:
```bash
# إعادة تعيين شاملة
python reset_password.py quick
```

### في حالة تعطل قاعدة البيانات:
```bash
# إعادة إنشاء قاعدة البيانات
python manage.py migrate
python manage.py createsuperuser
```

### في حالة عدم عمل الأدوات:
```bash
# الطريقة اليدوية
python manage.py shell
>>> from django.contrib.auth.models import User
>>> user = User.objects.get(username='admin')
>>> user.set_password('admin123')
>>> user.save()
```

---

## 📞 الدعم والمساعدة

### الملفات المهمة:
- `reset_password.py` - أداة إدارة المستخدمين
- `reset_login.bat` - إعادة تعيين سريعة
- `USER_MANAGEMENT_GUIDE.md` - هذا الدليل

### معلومات النظام:
- **المطور:** خالد شجاع
- **النظام:** نظام إدارة العملاء والديون
- **الإصدار:** 1.0.0

---

## 🎯 الخلاصة

### للوصول السريع:
1. **افتح المتصفح:** http://localhost:8000/login/
2. **استخدم البيانات:**
   - المدير: `admin` / `admin123`
   - المدير: `manager` / `manager123`

### لإعادة التعيين:
1. **انقر مرتين على:** `reset_login.bat`
2. **أو شغل:** `python reset_password.py quick`

### للإدارة المتقدمة:
1. **شغل:** `python reset_password.py`
2. **اختر العملية المطلوبة**

---

*تم تطوير هذا النظام بواسطة المبرمج خالد شجاع © 2025*
