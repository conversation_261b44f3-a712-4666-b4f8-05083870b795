<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}محلات أبو علاء - إدارة الديون{% endblock %}</title>

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        /* العلامة المائية */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-15deg);
            opacity: 0.15;
            z-index: -1;
            pointer-events: none;
            max-width: 400px;
            max-height: 400px;
            filter: grayscale(20%);
        }

        .watermark img {
            width: 100%;
            height: auto;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
        }

        /* حقوق المبرمج */
        .developer-footer {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 11px;
            z-index: 1000;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .developer-footer:hover {
            background: rgba(102, 126, 234, 1);
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .developer-footer i {
            margin-left: 5px;
        }

        @media print {
            .watermark {
                opacity: 0.08;
                z-index: 1;
            }
            .developer-footer {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .developer-footer {
                bottom: 5px;
                right: 5px;
                padding: 6px 12px;
                font-size: 10px;
            }
        }
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            background: linear-gradient(180deg, #34495e 0%, #2c3e50 100%);
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(52, 152, 219, 0.2);
            color: #3498db;
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link.active {
            background-color: #3498db;
            color: white;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #21618c 100%);
            transform: translateY(-2px);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .alert {
            border: none;
            border-radius: 15px;
        }
        
        .logo {
            max-height: 40px;
            margin-left: 10px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-store me-2"></i>
                محلات أبو علاء
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'change_password' %}">
                                    <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'customer_list' %}">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'debt_list' %}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'payment_list' %}">
                                <i class="fas fa-credit-card me-2"></i>
                                الدفعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'invoice_list' %}">
                                <i class="fas fa-file-invoice me-2"></i>
                                الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'reports_dashboard' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>

                        <hr class="sidebar-divider">

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'currency_settings' %}">
                                <i class="fas fa-coins me-2"></i>
                                إعدادات العملة
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'logo_settings' %}">
                                <i class="fas fa-image me-2"></i>
                                إعدادات الشعار
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'change_password' %}">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'backup_dashboard' %}">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                النسخ الاحتياطي
                            </a>
                        </li>
                        {% if user.is_superuser %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'admin:index' %}">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                {% if messages %}
                    <div class="mt-3">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- العلامة المائية -->
    {% load static %}
    {% if company_settings.logo %}
        <div class="watermark">
            <img src="{{ company_settings.logo.url }}" alt="علامة مائية" class="img-fluid">
        </div>
    {% endif %}

    <!-- حقوق المبرمج -->
    <div class="developer-footer">
        <i class="fas fa-code"></i>
        تطوير: المبرمج خالد شجاع © 2025
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
