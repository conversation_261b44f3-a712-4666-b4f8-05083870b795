# إعداد Google Drive للنسخ الاحتياطي

## الخطوات المطلوبة:

### 1. إنشاء مشروع في Google Cloud Console
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. تأكد من أن المشروع نشط

### 2. تفعيل Google Drive API
1. في Google Cloud Console، اذهب إلى "APIs & Services" > "Library"
2. ابحث عن "Google Drive API"
3. اضغط على "Enable"

### 3. إنشاء Service Account
1. اذهب إلى "APIs & Services" > "Credentials"
2. اضغط على "Create Credentials" > "Service Account"
3. أدخل اسم للحساب (مثل: abu-alaa-backup)
4. <PERSON><PERSON><PERSON><PERSON> "Create and Continue"
5. في "Grant this service account access to project"، اختر دور "Editor"
6. اضغ<PERSON> "Continue" ثم "Done"

### 4. إنشاء مفتاح JSON
1. في صفحة "Credentials"، اضغط على Service Account الذي أنشأته
2. اذهب إلى تبويب "Keys"
3. اضغط "Add Key" > "Create new key"
4. اختر "JSON" واضغط "Create"
5. سيتم تحميل ملف JSON - احفظه في مجلد المشروع

### 5. إنشاء مجلد في Google Drive
1. اذهب إلى [Google Drive](https://drive.google.com/)
2. أنشئ مجلد جديد للنسخ الاحتياطية (مثل: "Abu Alaa Backups")
3. انسخ ID المجلد من الرابط (الجزء الأخير بعد /folders/)

### 6. مشاركة المجلد مع Service Account
1. اضغط بالزر الأيمن على المجلد > "Share"
2. أضف البريد الإلكتروني للـ Service Account (موجود في ملف JSON)
3. أعطه صلاحية "Editor"

### 7. إعداد Django Settings
أضف هذه الإعدادات إلى ملف `settings.py`:

```python
# إعدادات Google Drive
GOOGLE_DRIVE_CREDENTIALS = BASE_DIR / 'path/to/your/credentials.json'
GOOGLE_DRIVE_FOLDER_ID = 'your_folder_id_here'
```

### 8. اختبار الإعداد
1. اذهب إلى صفحة النسخ الاحتياطي في النظام
2. جرب إنشاء نسخة احتياطية ورفعها إلى Google Drive
3. تأكد من ظهور الملف في المجلد المحدد

## ملاحظات مهمة:
- احتفظ بملف JSON في مكان آمن ولا تشاركه
- تأكد من أن ملف JSON غير مدرج في نظام التحكم بالإصدارات (Git)
- يمكنك تغيير صلاحيات Service Account حسب الحاجة
- المجلد يجب أن يكون مشارك مع Service Account

## استكشاف الأخطاء:
- **خطأ في المصادقة**: تأكد من صحة مسار ملف JSON
- **خطأ في الرفع**: تأكد من مشاركة المجلد مع Service Account
- **خطأ في الصلاحيات**: تأكد من أن Service Account له صلاحية Editor
