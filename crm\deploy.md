# دليل النشر على Google Cloud Run

## المتطلبات المسبقة

1. <PERSON>س<PERSON><PERSON> Google Cloud Platform
2. تث<PERSON><PERSON><PERSON> Google Cloud SDK
3. قاعدة بيانات PostgreSQL (Cloud SQL)

## خطوات النشر

### 1. إعداد قاعدة البيانات

```bash
# إنشاء instance لـ Cloud SQL
gcloud sql instances create abu-alaa-db \
    --database-version=POSTGRES_13 \
    --tier=db-f1-micro \
    --region=us-central1

# إنشاء قاعدة البيانات
gcloud sql databases create abu_alaa_crm --instance=abu-alaa-db

# إنشاء مستخدم
gcloud sql users create abu_alaa_user \
    --instance=abu-alaa-db \
    --password=your-secure-password
```

### 2. إعداد متغيرات البيئة

قم بتحديث ملف `.env` أو متغيرات البيئة:

```bash
DEBUG=False
SECRET_KEY=your-very-secure-secret-key-here
ALLOWED_HOSTS=your-domain.com,*.run.app
DATABASE_URL=**********************************************************************************************
```

### 3. بناء ونشر التطبيق

```bash
# تسجيل الدخول إلى Google Cloud
gcloud auth login

# تعيين المشروع
gcloud config set project YOUR_PROJECT_ID

# بناء الصورة
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/abu-alaa-crm

# النشر على Cloud Run
gcloud run deploy abu-alaa-crm \
    --image gcr.io/YOUR_PROJECT_ID/abu-alaa-crm \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --add-cloudsql-instances YOUR_PROJECT_ID:us-central1:abu-alaa-db \
    --set-env-vars DEBUG=False \
    --set-env-vars SECRET_KEY=your-secret-key \
    --set-env-vars DATABASE_URL=********************************************************************************************************
```

### 4. تشغيل migrations

```bash
# الحصول على URL الخدمة
SERVICE_URL=$(gcloud run services describe abu-alaa-crm --platform managed --region us-central1 --format 'value(status.url)')

# تشغيل migrations
gcloud run jobs create migrate-job \
    --image gcr.io/YOUR_PROJECT_ID/abu-alaa-crm \
    --command python \
    --args manage.py,migrate \
    --add-cloudsql-instances YOUR_PROJECT_ID:us-central1:abu-alaa-db \
    --set-env-vars DATABASE_URL=********************************************************************************************************

gcloud run jobs execute migrate-job --region us-central1
```

### 5. إنشاء superuser

```bash
# إنشاء job لإنشاء superuser
gcloud run jobs create create-superuser-job \
    --image gcr.io/YOUR_PROJECT_ID/abu-alaa-crm \
    --command python \
    --args manage.py,createsuperuser \
    --add-cloudsql-instances YOUR_PROJECT_ID:us-central1:abu-alaa-db \
    --set-env-vars DATABASE_URL=********************************************************************************************************

gcloud run jobs execute create-superuser-job --region us-central1
```

## الأمان

1. استخدم HTTPS دائماً
2. قم بتعيين SECRET_KEY قوي
3. استخدم كلمات مرور قوية لقاعدة البيانات
4. قم بتحديث ALLOWED_HOSTS ليشمل النطاق الخاص بك فقط
5. فعل SSL لقاعدة البيانات

## المراقبة

- استخدم Google Cloud Monitoring لمراقبة الأداء
- فعل Cloud Logging لتتبع الأخطاء
- قم بإعداد تنبيهات للأخطاء الحرجة

## النسخ الاحتياطي

```bash
# إنشاء نسخة احتياطية تلقائية
gcloud sql backups create --instance=abu-alaa-db
```

## التحديثات

```bash
# لتحديث التطبيق
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/abu-alaa-crm
gcloud run deploy abu-alaa-crm --image gcr.io/YOUR_PROJECT_ID/abu-alaa-crm --platform managed --region us-central1
```
