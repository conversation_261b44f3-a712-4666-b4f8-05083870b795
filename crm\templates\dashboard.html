{% extends 'base.html' %}
{% load currency_tags %}

{% block title %}لوحة التحكم - محلات أبو علاء{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي العملاء</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">إجمالي المدفوع</div>
                        <div class="h5 mb-0 font-weight-bold">{{ total_paid|currency }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">المبلغ المتبقي</div>
                        <div class="h5 mb-0 font-weight-bold">{{ remaining_debt|currency }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">ديون متأخرة</div>
                        <div class="h5 mb-0 font-weight-bold">{{ overdue_debts }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسم البياني -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    الديون والدفعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- الإشعارات والتنبيهات -->
{% if overdue_debts > 0 or due_soon > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    التنبيهات والإشعارات
                </h5>
            </div>
            <div class="card-body">
                {% if overdue_debts > 0 %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه!</strong> يوجد {{ overdue_debts }} دين متأخر عن موعد الاستحقاق.
                </div>
                {% endif %}
                
                {% if due_soon > 0 %}
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-clock me-2"></i>
                    <strong>تذكير!</strong> يوجد {{ due_soon }} دين مستحق خلال الأسبوع القادم.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- آخر الأنشطة -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>
                    آخر الديون المضافة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_debts %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for debt in recent_debts %}
                            <tr>
                                <td>{{ debt.customer.name }}</td>
                                <td>{{ debt.amount|floatformat:2 }} ريال</td>
                                <td>{{ debt.created_date|date:"Y-m-d" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد ديون مضافة حديثاً</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    آخر الدفعات
                </h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.debt.customer.name }}</td>
                                <td>{{ payment.amount|floatformat:2 }} ريال</td>
                                <td>{{ payment.payment_date|date:"Y-m-d" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد دفعات حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// الرسم البياني الشهري
const ctx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = {{ monthly_data|safe }};

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'الديون',
            data: monthlyData.map(item => item.debts),
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            tension: 0.4
        }, {
            label: 'الدفعات',
            data: monthlyData.map(item => item.payments),
            borderColor: '#27ae60',
            backgroundColor: 'rgba(39, 174, 96, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + ' ريال';
                    }
                }
            }
        }
    }
});
</script>
{% endblock %}
