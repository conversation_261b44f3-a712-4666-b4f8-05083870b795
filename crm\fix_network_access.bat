@echo off
chcp 65001 > nul
title إصلاح الوصول الشبكي - محلات أبو علاء

echo.
echo ========================================
echo    🔧 إصلاح الوصول الشبكي الشامل
echo    Complete Network Access Fix
echo    محلات أبو علاء - Abu Alaa Stores
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 تشخيص المشكلة...
python diagnose_network.py > diagnosis.txt 2>&1

echo.
echo 🔧 الخطوة 1: إصلاح إعدادات Django...
echo ALLOWED_HOSTS=* > .env.temp
echo DEBUG=True >> .env.temp
echo SECRET_KEY=django-insecure-#mehl_u=(lm^k3rbp0lc(55ma$o=9!7!c2j#t-5+%yg0i2fxbj >> .env.temp
echo DATABASE_URL=sqlite:///db.sqlite3 >> .env.temp
move .env.temp .env
echo ✅ تم تحديث ALLOWED_HOSTS

echo.
echo 🔧 الخطوة 2: إصلاح Windows Firewall...
echo ⚠️ يتطلب صلاحيات المدير...

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ صلاحيات المدير متاحة
    
    echo 📥 إضافة قاعدة Firewall للمنفذ 8000...
    netsh advfirewall firewall delete rule name="Abu Alaa CRM - Port 8000" >nul 2>&1
    netsh advfirewall firewall add rule name="Abu Alaa CRM - Port 8000" dir=in action=allow protocol=TCP localport=8000
    
    echo 🐍 إضافة قاعدة لتطبيق Python...
    netsh advfirewall firewall delete rule name="Abu Alaa CRM - Python" >nul 2>&1
    netsh advfirewall firewall add rule name="Abu Alaa CRM - Python" dir=in action=allow program="python.exe"
    
    echo ✅ تم إصلاح Firewall
) else (
    echo ❌ صلاحيات المدير غير متاحة
    echo 💡 شغل الملف كمدير (Right-click > Run as Administrator)
    echo 💡 أو شغل fix_firewall.bat كمدير
)

echo.
echo 🔧 الخطوة 3: إعادة تشغيل الخادم...
echo ⏹️ إيقاف الخادم الحالي...
taskkill /f /im python.exe >nul 2>&1

echo ⏳ انتظار 3 ثوان...
timeout /t 3 /nobreak >nul

echo 🚀 بدء الخادم الجديد...
start "Abu Alaa Server" cmd /k "python server_manager.py"

echo.
echo ⏳ انتظار بدء الخادم...
timeout /t 5 /nobreak >nul

echo.
echo 🧪 اختبار الاتصال...
python get_network_info.py ip

echo.
echo ✅ تم إصلاح جميع المشاكل!
echo.
echo 📱 الآن يمكن الوصول من الأجهزة الأخرى:
echo    🔗 http://************:8000
echo.
echo 🧪 لاختبار الاتصال من جهاز آخر:
echo    python diagnose_network.py test
echo.

pause
