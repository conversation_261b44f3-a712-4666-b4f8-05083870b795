@echo off
chcp 65001 > nul
title إصلاح Firewall للوصول الشبكي - محلات أبو علاء

echo.
echo ========================================
echo    🔥 إصلاح إعدادات Firewall
echo    Firewall Fix for Network Access
echo    محلات أبو علاء - Abu Alaa Stores
echo ========================================
echo.

echo ⚠️ هذا الملف يحتاج تشغيل كمدير (Run as Administrator)
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل الملف بصلاحيات المدير
) else (
    echo ❌ يرجى تشغيل الملف كمدير (Right-click > Run as Administrator)
    pause
    exit /b 1
)

echo.
echo 🔧 جاري إضافة قواعد Firewall...
echo.

:: إضافة قاعدة للمنفذ 8000 - Inbound
echo 📥 إضافة قاعدة Inbound للمنفذ 8000...
netsh advfirewall firewall add rule name="Abu Alaa CRM - Port 8000 Inbound" dir=in action=allow protocol=TCP localport=8000

:: إضافة قاعدة للمنفذ 8000 - Outbound
echo 📤 إضافة قاعدة Outbound للمنفذ 8000...
netsh advfirewall firewall add rule name="Abu Alaa CRM - Port 8000 Outbound" dir=out action=allow protocol=TCP localport=8000

:: إضافة قاعدة لـ Python
echo 🐍 إضافة قاعدة لتطبيق Python...
netsh advfirewall firewall add rule name="Abu Alaa CRM - Python" dir=in action=allow program="python.exe"

:: إضافة قاعدة للشبكة المحلية
echo 🏠 إضافة قاعدة للشبكة المحلية...
netsh advfirewall firewall add rule name="Abu Alaa CRM - Local Network" dir=in action=allow protocol=TCP localport=8000 remoteip=***********/16,10.0.0.0/8,**********/12

echo.
echo ✅ تم إضافة جميع قواعد Firewall بنجاح!
echo.

echo 📋 القواعد المضافة:
echo    - المنفذ 8000 (Inbound/Outbound)
echo    - تطبيق Python
echo    - الشبكة المحلية
echo.

echo 🔍 فحص القواعد الحالية...
netsh advfirewall firewall show rule name="Abu Alaa CRM - Port 8000 Inbound"

echo.
echo 🎯 الآن يمكن الوصول للنظام من الأجهزة الأخرى!
echo.
echo 📱 للاختبار من الهاتف:
echo    1. تأكد من اتصال الهاتف بنفس WiFi
echo    2. افتح المتصفح
echo    3. اذهب إلى: http://************:8000
echo.

pause
